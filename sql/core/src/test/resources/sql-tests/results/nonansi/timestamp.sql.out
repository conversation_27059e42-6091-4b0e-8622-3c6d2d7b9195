-- Automatically generated by SQLQueryTestSuite
-- !query
select timestamp '2019-01-01\t'
-- !query schema
struct<TIMESTAMP '2019-01-01 00:00:00':timestamp>
-- !query output
2019-01-01 00:00:00


-- !query
select timestamp '2019-01-01中文'
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "INVALID_TYPED_LITERAL",
  "sqlState" : "42604",
  "messageParameters" : {
    "value" : "'2019-01-01中文'",
    "valueType" : "\"TIMESTAMP\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 31,
    "fragment" : "timestamp '2019-01-01中文'"
  } ]
}


-- !query
select timestamp'4294967297'
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "INVALID_TYPED_LITERAL",
  "sqlState" : "42604",
  "messageParameters" : {
    "value" : "'4294967297'",
    "valueType" : "\"TIMESTAMP\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 28,
    "fragment" : "timestamp'4294967297'"
  } ]
}


-- !query
select timestamp'2021-01-01T12:30:4294967297.123456'
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "INVALID_TYPED_LITERAL",
  "sqlState" : "42604",
  "messageParameters" : {
    "value" : "'2021-01-01T12:30:4294967297.123456'",
    "valueType" : "\"TIMESTAMP\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 52,
    "fragment" : "timestamp'2021-01-01T12:30:4294967297.123456'"
  } ]
}


-- !query
select current_timestamp = current_timestamp
-- !query schema
struct<(current_timestamp() = current_timestamp()):boolean>
-- !query output
true


-- !query
select current_timestamp() = current_timestamp()
-- !query schema
struct<(current_timestamp() = current_timestamp()):boolean>
-- !query output
true


-- !query
select localtimestamp() = localtimestamp()
-- !query schema
struct<(localtimestamp() = localtimestamp()):boolean>
-- !query output
true


-- !query
SELECT make_timestamp(2021, 07, 11, 6, 30, 45.678)
-- !query schema
struct<make_timestamp(2021, 7, 11, 6, 30, 45.678):timestamp>
-- !query output
2021-07-11 06:30:45.678


-- !query
SELECT make_timestamp(2021, 07, 11, 6, 30, 45.678, 'CET')
-- !query schema
struct<make_timestamp(2021, 7, 11, 6, 30, 45.678, CET):timestamp>
-- !query output
2021-07-10 21:30:45.678


-- !query
SELECT make_timestamp(2021, 07, 11, 6, 30, 60.007)
-- !query schema
struct<make_timestamp(2021, 7, 11, 6, 30, 60.007):timestamp>
-- !query output
NULL


-- !query
SELECT make_timestamp(1, 1, 1, 1, 1, 1)
-- !query schema
struct<make_timestamp(1, 1, 1, 1, 1, 1):timestamp>
-- !query output
0001-01-01 01:01:01


-- !query
SELECT make_timestamp(1, 1, 1, 1, 1, 60)
-- !query schema
struct<make_timestamp(1, 1, 1, 1, 1, 60):timestamp>
-- !query output
0001-01-01 01:02:00


-- !query
SELECT make_timestamp(1, 1, 1, 1, 1, 61)
-- !query schema
struct<make_timestamp(1, 1, 1, 1, 1, 61):timestamp>
-- !query output
NULL


-- !query
SELECT make_timestamp(1, 1, 1, 1, 1, null)
-- !query schema
struct<make_timestamp(1, 1, 1, 1, 1, NULL):timestamp>
-- !query output
NULL


-- !query
SELECT make_timestamp(1, 1, 1, 1, 1, 59.999999)
-- !query schema
struct<make_timestamp(1, 1, 1, 1, 1, 59.999999):timestamp>
-- !query output
0001-01-01 01:01:59.999999


-- !query
SELECT make_timestamp(1, 1, 1, 1, 1, 99.999999)
-- !query schema
struct<make_timestamp(1, 1, 1, 1, 1, 99.999999):timestamp>
-- !query output
NULL


-- !query
SELECT make_timestamp(1, 1, 1, 1, 1, 999.999999)
-- !query schema
struct<make_timestamp(1, 1, 1, 1, 1, 999.999999):timestamp>
-- !query output
NULL


-- !query
select TIMESTAMP_SECONDS(1230219000),TIMESTAMP_SECONDS(-1230219000),TIMESTAMP_SECONDS(null)
-- !query schema
struct<timestamp_seconds(1230219000):timestamp,timestamp_seconds(-1230219000):timestamp,timestamp_seconds(NULL):timestamp>
-- !query output
2008-12-25 07:30:00	1931-01-07 00:30:00	NULL


-- !query
select TIMESTAMP_SECONDS(1.23), TIMESTAMP_SECONDS(1.23d), TIMESTAMP_SECONDS(FLOAT(1.23))
-- !query schema
struct<timestamp_seconds(1.23):timestamp,timestamp_seconds(1.23):timestamp,timestamp_seconds(1.23):timestamp>
-- !query output
1969-12-31 16:00:01.23	1969-12-31 16:00:01.23	1969-12-31 16:00:01.23


-- !query
select TIMESTAMP_MILLIS(1230219000123),TIMESTAMP_MILLIS(-1230219000123),TIMESTAMP_MILLIS(null)
-- !query schema
struct<timestamp_millis(1230219000123):timestamp,timestamp_millis(-1230219000123):timestamp,timestamp_millis(NULL):timestamp>
-- !query output
2008-12-25 07:30:00.123	1931-01-07 00:29:59.877	NULL


-- !query
select TIMESTAMP_MICROS(1230219000123123),TIMESTAMP_MICROS(-1230219000123123),TIMESTAMP_MICROS(null)
-- !query schema
struct<timestamp_micros(1230219000123123):timestamp,timestamp_micros(-1230219000123123):timestamp,timestamp_micros(NULL):timestamp>
-- !query output
2008-12-25 07:30:00.123123	1931-01-07 00:29:59.876877	NULL


-- !query
select TIMESTAMP_SECONDS(1230219000123123)
-- !query schema
struct<>
-- !query output
java.lang.ArithmeticException
long overflow


-- !query
select TIMESTAMP_SECONDS(-1230219000123123)
-- !query schema
struct<>
-- !query output
java.lang.ArithmeticException
long overflow


-- !query
select TIMESTAMP_MILLIS(92233720368547758)
-- !query schema
struct<>
-- !query output
java.lang.ArithmeticException
long overflow


-- !query
select TIMESTAMP_MILLIS(-92233720368547758)
-- !query schema
struct<>
-- !query output
java.lang.ArithmeticException
long overflow


-- !query
select TIMESTAMP_SECONDS(0.1234567)
-- !query schema
struct<>
-- !query output
java.lang.ArithmeticException
Rounding necessary


-- !query
select TIMESTAMP_SECONDS(0.1234567d), TIMESTAMP_SECONDS(FLOAT(0.1234567))
-- !query schema
struct<timestamp_seconds(0.1234567):timestamp,timestamp_seconds(0.1234567):timestamp>
-- !query output
1969-12-31 16:00:00.123456	1969-12-31 16:00:00.123456


-- !query
create temporary view ttf1 as select * from values
  (1, 2),
  (2, 3)
  as ttf1(`current_date`, `current_timestamp`)
-- !query schema
struct<>
-- !query output



-- !query
select typeof(current_date), typeof(current_timestamp) from ttf1
-- !query schema
struct<typeof(current_date):string,typeof(current_timestamp):string>
-- !query output
int	int
int	int


-- !query
create temporary view ttf2 as select * from values
  (1, 2),
  (2, 3)
  as ttf2(a, b)
-- !query schema
struct<>
-- !query output



-- !query
select current_date = current_date(), current_timestamp = current_timestamp(), a, b from ttf2
-- !query schema
struct<(current_date() = current_date()):boolean,(current_timestamp() = current_timestamp()):boolean,a:int,b:int>
-- !query output
true	true	1	2
true	true	2	3


-- !query
select a, b from ttf2 order by a, current_date
-- !query schema
struct<a:int,b:int>
-- !query output
1	2
2	3


-- !query
select UNIX_SECONDS(timestamp'2020-12-01 14:30:08Z'), UNIX_SECONDS(timestamp'2020-12-01 14:30:08.999999Z'), UNIX_SECONDS(null)
-- !query schema
struct<unix_seconds(TIMESTAMP '2020-12-01 06:30:08'):bigint,unix_seconds(TIMESTAMP '2020-12-01 06:30:08.999999'):bigint,unix_seconds(NULL):bigint>
-- !query output
1606833008	1606833008	NULL


-- !query
select UNIX_MILLIS(timestamp'2020-12-01 14:30:08Z'), UNIX_MILLIS(timestamp'2020-12-01 14:30:08.999999Z'), UNIX_MILLIS(null)
-- !query schema
struct<unix_millis(TIMESTAMP '2020-12-01 06:30:08'):bigint,unix_millis(TIMESTAMP '2020-12-01 06:30:08.999999'):bigint,unix_millis(NULL):bigint>
-- !query output
1606833008000	1606833008999	NULL


-- !query
select UNIX_MICROS(timestamp'2020-12-01 14:30:08Z'), UNIX_MICROS(timestamp'2020-12-01 14:30:08.999999Z'), UNIX_MICROS(null)
-- !query schema
struct<unix_micros(TIMESTAMP '2020-12-01 06:30:08'):bigint,unix_micros(TIMESTAMP '2020-12-01 06:30:08.999999'):bigint,unix_micros(NULL):bigint>
-- !query output
1606833008000000	1606833008999999	NULL


-- !query
select to_timestamp(null), to_timestamp('2016-12-31 00:12:00'), to_timestamp('2016-12-31', 'yyyy-MM-dd')
-- !query schema
struct<to_timestamp(NULL):timestamp,to_timestamp(2016-12-31 00:12:00):timestamp,to_timestamp(2016-12-31, yyyy-MM-dd):timestamp>
-- !query output
NULL	2016-12-31 00:12:00	2016-12-31 00:00:00


-- !query
select to_timestamp(1)
-- !query schema
struct<to_timestamp(1):timestamp>
-- !query output
1969-12-31 16:00:01


-- !query
select to_timestamp('2019-10-06 10:11:12.', 'yyyy-MM-dd HH:mm:ss.SSSSSS[zzz]')
-- !query schema
struct<to_timestamp(2019-10-06 10:11:12., yyyy-MM-dd HH:mm:ss.SSSSSS[zzz]):timestamp>
-- !query output
NULL


-- !query
select to_timestamp('2019-10-06 10:11:12.0', 'yyyy-MM-dd HH:mm:ss.SSSSSS[zzz]')
-- !query schema
struct<to_timestamp(2019-10-06 10:11:12.0, yyyy-MM-dd HH:mm:ss.SSSSSS[zzz]):timestamp>
-- !query output
2019-10-06 10:11:12


-- !query
select to_timestamp('2019-10-06 10:11:12.1', 'yyyy-MM-dd HH:mm:ss.SSSSSS[zzz]')
-- !query schema
struct<to_timestamp(2019-10-06 10:11:12.1, yyyy-MM-dd HH:mm:ss.SSSSSS[zzz]):timestamp>
-- !query output
2019-10-06 10:11:12.1


-- !query
select to_timestamp('2019-10-06 10:11:12.12', 'yyyy-MM-dd HH:mm:ss.SSSSSS[zzz]')
-- !query schema
struct<to_timestamp(2019-10-06 10:11:12.12, yyyy-MM-dd HH:mm:ss.SSSSSS[zzz]):timestamp>
-- !query output
2019-10-06 10:11:12.12


-- !query
select to_timestamp('2019-10-06 10:11:12.123UTC', 'yyyy-MM-dd HH:mm:ss.SSSSSS[zzz]')
-- !query schema
struct<to_timestamp(2019-10-06 10:11:12.123UTC, yyyy-MM-dd HH:mm:ss.SSSSSS[zzz]):timestamp>
-- !query output
2019-10-06 03:11:12.123


-- !query
select to_timestamp('2019-10-06 10:11:12.1234', 'yyyy-MM-dd HH:mm:ss.SSSSSS[zzz]')
-- !query schema
struct<to_timestamp(2019-10-06 10:11:12.1234, yyyy-MM-dd HH:mm:ss.SSSSSS[zzz]):timestamp>
-- !query output
2019-10-06 10:11:12.1234


-- !query
select to_timestamp('2019-10-06 10:11:12.12345CST', 'yyyy-MM-dd HH:mm:ss.SSSSSS[zzz]')
-- !query schema
struct<to_timestamp(2019-10-06 10:11:12.12345CST, yyyy-MM-dd HH:mm:ss.SSSSSS[zzz]):timestamp>
-- !query output
2019-10-06 08:11:12.12345


-- !query
select to_timestamp('2019-10-06 10:11:12.123456PST', 'yyyy-MM-dd HH:mm:ss.SSSSSS[zzz]')
-- !query schema
struct<to_timestamp(2019-10-06 10:11:12.123456PST, yyyy-MM-dd HH:mm:ss.SSSSSS[zzz]):timestamp>
-- !query output
2019-10-06 10:11:12.123456


-- !query
select to_timestamp('2019-10-06 10:11:12.1234567PST', 'yyyy-MM-dd HH:mm:ss.SSSSSS[zzz]')
-- !query schema
struct<to_timestamp(2019-10-06 10:11:12.1234567PST, yyyy-MM-dd HH:mm:ss.SSSSSS[zzz]):timestamp>
-- !query output
NULL


-- !query
select to_timestamp('123456 2019-10-06 10:11:12.123456PST', 'SSSSSS yyyy-MM-dd HH:mm:ss.SSSSSS[zzz]')
-- !query schema
struct<to_timestamp(123456 2019-10-06 10:11:12.123456PST, SSSSSS yyyy-MM-dd HH:mm:ss.SSSSSS[zzz]):timestamp>
-- !query output
2019-10-06 10:11:12.123456


-- !query
select to_timestamp('223456 2019-10-06 10:11:12.123456PST', 'SSSSSS yyyy-MM-dd HH:mm:ss.SSSSSS[zzz]')
-- !query schema
struct<to_timestamp(223456 2019-10-06 10:11:12.123456PST, SSSSSS yyyy-MM-dd HH:mm:ss.SSSSSS[zzz]):timestamp>
-- !query output
NULL


-- !query
select to_timestamp('2019-10-06 10:11:12.1234', 'yyyy-MM-dd HH:mm:ss.[SSSSSS]')
-- !query schema
struct<to_timestamp(2019-10-06 10:11:12.1234, yyyy-MM-dd HH:mm:ss.[SSSSSS]):timestamp>
-- !query output
2019-10-06 10:11:12.1234


-- !query
select to_timestamp('2019-10-06 10:11:12.123', 'yyyy-MM-dd HH:mm:ss[.SSSSSS]')
-- !query schema
struct<to_timestamp(2019-10-06 10:11:12.123, yyyy-MM-dd HH:mm:ss[.SSSSSS]):timestamp>
-- !query output
2019-10-06 10:11:12.123


-- !query
select to_timestamp('2019-10-06 10:11:12', 'yyyy-MM-dd HH:mm:ss[.SSSSSS]')
-- !query schema
struct<to_timestamp(2019-10-06 10:11:12, yyyy-MM-dd HH:mm:ss[.SSSSSS]):timestamp>
-- !query output
2019-10-06 10:11:12


-- !query
select to_timestamp('2019-10-06 10:11:12.12', 'yyyy-MM-dd HH:mm[:ss.SSSSSS]')
-- !query schema
struct<to_timestamp(2019-10-06 10:11:12.12, yyyy-MM-dd HH:mm[:ss.SSSSSS]):timestamp>
-- !query output
2019-10-06 10:11:12.12


-- !query
select to_timestamp('2019-10-06 10:11', 'yyyy-MM-dd HH:mm[:ss.SSSSSS]')
-- !query schema
struct<to_timestamp(2019-10-06 10:11, yyyy-MM-dd HH:mm[:ss.SSSSSS]):timestamp>
-- !query output
2019-10-06 10:11:00


-- !query
select to_timestamp("2019-10-06S10:11:12.12345", "yyyy-MM-dd'S'HH:mm:ss.SSSSSS")
-- !query schema
struct<to_timestamp(2019-10-06S10:11:12.12345, yyyy-MM-dd'S'HH:mm:ss.SSSSSS):timestamp>
-- !query output
2019-10-06 10:11:12.12345


-- !query
select to_timestamp("12.12342019-10-06S10:11", "ss.SSSSyyyy-MM-dd'S'HH:mm")
-- !query schema
struct<to_timestamp(12.12342019-10-06S10:11, ss.SSSSyyyy-MM-dd'S'HH:mm):timestamp>
-- !query output
2019-10-06 10:11:12.1234


-- !query
select to_timestamp("12.1232019-10-06S10:11", "ss.SSSSyyyy-MM-dd'S'HH:mm")
-- !query schema
struct<to_timestamp(12.1232019-10-06S10:11, ss.SSSSyyyy-MM-dd'S'HH:mm):timestamp>
-- !query output
NULL


-- !query
select to_timestamp("12.1232019-10-06S10:11", "ss.SSSSyy-MM-dd'S'HH:mm")
-- !query schema
struct<to_timestamp(12.1232019-10-06S10:11, ss.SSSSyy-MM-dd'S'HH:mm):timestamp>
-- !query output
NULL


-- !query
select to_timestamp("12.1234019-10-06S10:11", "ss.SSSSy-MM-dd'S'HH:mm")
-- !query schema
struct<to_timestamp(12.1234019-10-06S10:11, ss.SSSSy-MM-dd'S'HH:mm):timestamp>
-- !query output
0019-10-06 10:11:12.1234


-- !query
select to_timestamp("2019-10-06S", "yyyy-MM-dd'S'")
-- !query schema
struct<to_timestamp(2019-10-06S, yyyy-MM-dd'S'):timestamp>
-- !query output
2019-10-06 00:00:00


-- !query
select to_timestamp("S2019-10-06", "'S'yyyy-MM-dd")
-- !query schema
struct<to_timestamp(S2019-10-06, 'S'yyyy-MM-dd):timestamp>
-- !query output
2019-10-06 00:00:00


-- !query
select to_timestamp("2019-10-06T10:11:12'12", "yyyy-MM-dd'T'HH:mm:ss''SSSS")
-- !query schema
struct<to_timestamp(2019-10-06T10:11:12'12, yyyy-MM-dd'T'HH:mm:ss''SSSS):timestamp>
-- !query output
2019-10-06 10:11:12.12


-- !query
select to_timestamp("2019-10-06T10:11:12'", "yyyy-MM-dd'T'HH:mm:ss''")
-- !query schema
struct<to_timestamp(2019-10-06T10:11:12', yyyy-MM-dd'T'HH:mm:ss''):timestamp>
-- !query output
2019-10-06 10:11:12


-- !query
select to_timestamp("'2019-10-06T10:11:12", "''yyyy-MM-dd'T'HH:mm:ss")
-- !query schema
struct<to_timestamp('2019-10-06T10:11:12, ''yyyy-MM-dd'T'HH:mm:ss):timestamp>
-- !query output
2019-10-06 10:11:12


-- !query
select to_timestamp("P2019-10-06T10:11:12", "'P'yyyy-MM-dd'T'HH:mm:ss")
-- !query schema
struct<to_timestamp(P2019-10-06T10:11:12, 'P'yyyy-MM-dd'T'HH:mm:ss):timestamp>
-- !query output
2019-10-06 10:11:12


-- !query
select to_timestamp("16", "dd")
-- !query schema
struct<to_timestamp(16, dd):timestamp>
-- !query output
1970-01-16 00:00:00


-- !query
select to_timestamp("02-29", "MM-dd")
-- !query schema
struct<to_timestamp(02-29, MM-dd):timestamp>
-- !query output
NULL


-- !query
select to_timestamp("2019 40", "yyyy mm")
-- !query schema
struct<to_timestamp(2019 40, yyyy mm):timestamp>
-- !query output
2019-01-01 00:40:00


-- !query
select to_timestamp("2019 10:10:10", "yyyy hh:mm:ss")
-- !query schema
struct<to_timestamp(2019 10:10:10, yyyy hh:mm:ss):timestamp>
-- !query output
2019-01-01 10:10:10


-- !query
select timestamp'2011-11-11 11:11:11' - timestamp'2011-11-11 11:11:10'
-- !query schema
struct<(TIMESTAMP '2011-11-11 11:11:11' - TIMESTAMP '2011-11-11 11:11:10'):interval day to second>
-- !query output
0 00:00:01.000000000


-- !query
select date'2020-01-01' - timestamp'2019-10-06 10:11:12.345678'
-- !query schema
struct<(DATE '2020-01-01' - TIMESTAMP '2019-10-06 10:11:12.345678'):interval day to second>
-- !query output
86 13:48:47.654322000


-- !query
select timestamp'2019-10-06 10:11:12.345678' - date'2020-01-01'
-- !query schema
struct<(TIMESTAMP '2019-10-06 10:11:12.345678' - DATE '2020-01-01'):interval day to second>
-- !query output
-86 13:48:47.654322000


-- !query
select timestamp'2011-11-11 11:11:11' - '2011-11-11 11:11:10'
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.catalyst.ExtendedAnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"2011-11-11 11:11:10\"",
    "inputType" : "\"STRING\"",
    "paramIndex" : "second",
    "requiredType" : "\"(TIMESTAMP OR TIMESTAMP WITHOUT TIME ZONE)\"",
    "sqlExpr" : "\"(TIMESTAMP '2011-11-11 11:11:11' - 2011-11-11 11:11:10)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 61,
    "fragment" : "timestamp'2011-11-11 11:11:11' - '2011-11-11 11:11:10'"
  } ]
}


-- !query
select '2011-11-11 11:11:11' - timestamp'2011-11-11 11:11:10'
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.catalyst.ExtendedAnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"2011-11-11 11:11:11\"",
    "inputType" : "\"STRING\"",
    "paramIndex" : "first",
    "requiredType" : "\"(TIMESTAMP OR TIMESTAMP WITHOUT TIME ZONE)\"",
    "sqlExpr" : "\"(2011-11-11 11:11:11 - TIMESTAMP '2011-11-11 11:11:10')\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 61,
    "fragment" : "'2011-11-11 11:11:11' - timestamp'2011-11-11 11:11:10'"
  } ]
}


-- !query
select timestamp'2011-11-11 11:11:11' - null
-- !query schema
struct<(TIMESTAMP '2011-11-11 11:11:11' - NULL):interval day to second>
-- !query output
NULL


-- !query
select null - timestamp'2011-11-11 11:11:11'
-- !query schema
struct<(NULL - TIMESTAMP '2011-11-11 11:11:11'):interval day to second>
-- !query output
NULL


-- !query
create temporary view ts_view as select '2011-11-11 11:11:11' str
-- !query schema
struct<>
-- !query output



-- !query
select str - timestamp'2011-11-11 11:11:11' from ts_view
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.catalyst.ExtendedAnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"str\"",
    "inputType" : "\"STRING\"",
    "paramIndex" : "first",
    "requiredType" : "\"(TIMESTAMP OR TIMESTAMP WITHOUT TIME ZONE)\"",
    "sqlExpr" : "\"(str - TIMESTAMP '2011-11-11 11:11:11')\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 43,
    "fragment" : "str - timestamp'2011-11-11 11:11:11'"
  } ]
}


-- !query
select timestamp'2011-11-11 11:11:11' - str from ts_view
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.catalyst.ExtendedAnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.UNEXPECTED_INPUT_TYPE",
  "sqlState" : "42K09",
  "messageParameters" : {
    "inputSql" : "\"str\"",
    "inputType" : "\"STRING\"",
    "paramIndex" : "second",
    "requiredType" : "\"(TIMESTAMP OR TIMESTAMP WITHOUT TIME ZONE)\"",
    "sqlExpr" : "\"(TIMESTAMP '2011-11-11 11:11:11' - str)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 43,
    "fragment" : "timestamp'2011-11-11 11:11:11' - str"
  } ]
}


-- !query
select timestamp'2011-11-11 11:11:11' + '1'
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.catalyst.ExtendedAnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"DOUBLE\"",
    "sqlExpr" : "\"(TIMESTAMP '2011-11-11 11:11:11' + 1)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 43,
    "fragment" : "timestamp'2011-11-11 11:11:11' + '1'"
  } ]
}


-- !query
select '1' + timestamp'2011-11-11 11:11:11'
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.catalyst.ExtendedAnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"DOUBLE\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(1 + TIMESTAMP '2011-11-11 11:11:11')\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 43,
    "fragment" : "'1' + timestamp'2011-11-11 11:11:11'"
  } ]
}


-- !query
select timestamp'2011-11-11 11:11:11' + null
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.catalyst.ExtendedAnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"TIMESTAMP\"",
    "right" : "\"VOID\"",
    "sqlExpr" : "\"(TIMESTAMP '2011-11-11 11:11:11' + NULL)\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 44,
    "fragment" : "timestamp'2011-11-11 11:11:11' + null"
  } ]
}


-- !query
select null + timestamp'2011-11-11 11:11:11'
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.catalyst.ExtendedAnalysisException
{
  "errorClass" : "DATATYPE_MISMATCH.BINARY_OP_DIFF_TYPES",
  "sqlState" : "42K09",
  "messageParameters" : {
    "left" : "\"VOID\"",
    "right" : "\"TIMESTAMP\"",
    "sqlExpr" : "\"(NULL + TIMESTAMP '2011-11-11 11:11:11')\""
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 44,
    "fragment" : "null + timestamp'2011-11-11 11:11:11'"
  } ]
}


-- !query
select timestamp'2011-11-11 11:11:11' + interval '2' day,
       timestamp'2011-11-11 11:11:11' - interval '2-2' year to month,
       timestamp'2011-11-11 11:11:11' + interval '-2' second,
       timestamp'2011-11-11 11:11:11' - interval '12:12:12.123456789' hour to second,
       - interval 2 years + timestamp'2011-11-11 11:11:11',
       interval '1 12' day to hour + timestamp'2011-11-11 11:11:11'
-- !query schema
struct<TIMESTAMP '2011-11-11 11:11:11' + INTERVAL '2' DAY:timestamp,TIMESTAMP '2011-11-11 11:11:11' - INTERVAL '2-2' YEAR TO MONTH:timestamp,TIMESTAMP '2011-11-11 11:11:11' + INTERVAL '-02' SECOND:timestamp,TIMESTAMP '2011-11-11 11:11:11' - INTERVAL '12:12:12.123456' HOUR TO SECOND:timestamp,TIMESTAMP '2011-11-11 11:11:11' + (- INTERVAL '2' YEAR):timestamp,TIMESTAMP '2011-11-11 11:11:11' + INTERVAL '1 12' DAY TO HOUR:timestamp>
-- !query output
2011-11-13 11:11:11	2009-09-11 11:11:11	2011-11-11 11:11:09	2011-11-10 22:58:58.876544	2009-11-11 11:11:11	2011-11-12 23:11:11


-- !query
select date '2012-01-01' - interval 3 hours,
       date '2012-01-01' + interval '12:12:12' hour to second,
       interval '2' minute + date '2012-01-01'
-- !query schema
struct<DATE '2012-01-01' - INTERVAL '03' HOUR:timestamp,DATE '2012-01-01' + INTERVAL '12:12:12' HOUR TO SECOND:timestamp,DATE '2012-01-01' + INTERVAL '02' MINUTE:timestamp>
-- !query output
2011-12-31 21:00:00	2012-01-01 12:12:12	2012-01-01 00:02:00


-- !query
select to_timestamp('2019-10-06 A', 'yyyy-MM-dd GGGGG')
-- !query schema
struct<>
-- !query output
org.apache.spark.SparkUpgradeException
{
  "errorClass" : "INCONSISTENT_BEHAVIOR_CROSS_VERSION.DATETIME_PATTERN_RECOGNITION",
  "sqlState" : "42K0B",
  "messageParameters" : {
    "config" : "\"spark.sql.legacy.timeParserPolicy\"",
    "docroot" : "https://spark.apache.org/docs/latest",
    "pattern" : "'yyyy-MM-dd GGGGG'"
  }
}


-- !query
select to_timestamp('22 05 2020 Friday', 'dd MM yyyy EEEEEE')
-- !query schema
struct<>
-- !query output
org.apache.spark.SparkUpgradeException
{
  "errorClass" : "INCONSISTENT_BEHAVIOR_CROSS_VERSION.DATETIME_PATTERN_RECOGNITION",
  "sqlState" : "42K0B",
  "messageParameters" : {
    "config" : "\"spark.sql.legacy.timeParserPolicy\"",
    "docroot" : "https://spark.apache.org/docs/latest",
    "pattern" : "'dd MM yyyy EEEEEE'"
  }
}


-- !query
select to_timestamp('22 05 2020 Friday', 'dd MM yyyy EEEEE')
-- !query schema
struct<>
-- !query output
org.apache.spark.SparkUpgradeException
{
  "errorClass" : "INCONSISTENT_BEHAVIOR_CROSS_VERSION.DATETIME_PATTERN_RECOGNITION",
  "sqlState" : "42K0B",
  "messageParameters" : {
    "config" : "\"spark.sql.legacy.timeParserPolicy\"",
    "docroot" : "https://spark.apache.org/docs/latest",
    "pattern" : "'dd MM yyyy EEEEE'"
  }
}


-- !query
select unix_timestamp('22 05 2020 Friday', 'dd MM yyyy EEEEE')
-- !query schema
struct<>
-- !query output
org.apache.spark.SparkUpgradeException
{
  "errorClass" : "INCONSISTENT_BEHAVIOR_CROSS_VERSION.DATETIME_PATTERN_RECOGNITION",
  "sqlState" : "42K0B",
  "messageParameters" : {
    "config" : "\"spark.sql.legacy.timeParserPolicy\"",
    "docroot" : "https://spark.apache.org/docs/latest",
    "pattern" : "'dd MM yyyy EEEEE'"
  }
}


-- !query
select from_json('{"t":"26/October/2015"}', 't Timestamp', map('timestampFormat', 'dd/MMMMM/yyyy'))
-- !query schema
struct<>
-- !query output
org.apache.spark.SparkUpgradeException
{
  "errorClass" : "INCONSISTENT_BEHAVIOR_CROSS_VERSION.DATETIME_PATTERN_RECOGNITION",
  "sqlState" : "42K0B",
  "messageParameters" : {
    "config" : "\"spark.sql.legacy.timeParserPolicy\"",
    "docroot" : "https://spark.apache.org/docs/latest",
    "pattern" : "'dd/MMMMM/yyyy'"
  }
}


-- !query
select from_csv('26/October/2015', 't Timestamp', map('timestampFormat', 'dd/MMMMM/yyyy'))
-- !query schema
struct<>
-- !query output
org.apache.spark.SparkUpgradeException
{
  "errorClass" : "INCONSISTENT_BEHAVIOR_CROSS_VERSION.DATETIME_PATTERN_RECOGNITION",
  "sqlState" : "42K0B",
  "messageParameters" : {
    "config" : "\"spark.sql.legacy.timeParserPolicy\"",
    "docroot" : "https://spark.apache.org/docs/latest",
    "pattern" : "'dd/MMMMM/yyyy'"
  }
}


-- !query
select timestampadd(MONTH, -1, timestamp'2022-02-14 01:02:03')
-- !query schema
struct<timestampadd(MONTH, -1, TIMESTAMP '2022-02-14 01:02:03'):timestamp>
-- !query output
2022-01-14 01:02:03


-- !query
select timestampadd(MINUTE, 58, timestamp'2022-02-14 01:02:03')
-- !query schema
struct<timestampadd(MINUTE, 58, TIMESTAMP '2022-02-14 01:02:03'):timestamp>
-- !query output
2022-02-14 02:00:03


-- !query
select timestampadd(YEAR, 1, date'2022-02-15')
-- !query schema
struct<timestampadd(YEAR, 1, DATE '2022-02-15'):timestamp>
-- !query output
2023-02-15 00:00:00


-- !query
select timestampadd(SECOND, -1, date'2022-02-15')
-- !query schema
struct<timestampadd(SECOND, -1, DATE '2022-02-15'):timestamp>
-- !query output
2022-02-14 23:59:59


-- !query
select timestampadd('MONTH', -1, timestamp'2022-02-14 01:02:03')
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "INVALID_PARAMETER_VALUE.DATETIME_UNIT",
  "sqlState" : "22023",
  "messageParameters" : {
    "functionName" : "`timestampadd`",
    "invalidValue" : "'MONTH'",
    "parameter" : "`unit`"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 64,
    "fragment" : "timestampadd('MONTH', -1, timestamp'2022-02-14 01:02:03')"
  } ]
}


-- !query
select timestampadd('SECOND', -1, date'2022-02-15')
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "INVALID_PARAMETER_VALUE.DATETIME_UNIT",
  "sqlState" : "22023",
  "messageParameters" : {
    "functionName" : "`timestampadd`",
    "invalidValue" : "'SECOND'",
    "parameter" : "`unit`"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 51,
    "fragment" : "timestampadd('SECOND', -1, date'2022-02-15')"
  } ]
}


-- !query
select timestampdiff(MONTH, timestamp'2022-02-14 01:02:03', timestamp'2022-01-14 01:02:03')
-- !query schema
struct<timestampdiff(MONTH, TIMESTAMP '2022-02-14 01:02:03', TIMESTAMP '2022-01-14 01:02:03'):bigint>
-- !query output
-1


-- !query
select timestampdiff(MINUTE, timestamp'2022-02-14 01:02:03', timestamp'2022-02-14 02:00:03')
-- !query schema
struct<timestampdiff(MINUTE, TIMESTAMP '2022-02-14 01:02:03', TIMESTAMP '2022-02-14 02:00:03'):bigint>
-- !query output
58


-- !query
select timestampdiff(YEAR, date'2022-02-15', date'2023-02-15')
-- !query schema
struct<timestampdiff(YEAR, DATE '2022-02-15', DATE '2023-02-15'):bigint>
-- !query output
1


-- !query
select timestampdiff(SECOND, date'2022-02-15', timestamp'2022-02-14 23:59:59')
-- !query schema
struct<timestampdiff(SECOND, DATE '2022-02-15', TIMESTAMP '2022-02-14 23:59:59'):bigint>
-- !query output
-1


-- !query
select timestampdiff('MINUTE', timestamp'2022-02-14 01:02:03', timestamp'2022-02-14 02:00:03')
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "INVALID_PARAMETER_VALUE.DATETIME_UNIT",
  "sqlState" : "22023",
  "messageParameters" : {
    "functionName" : "`timestampdiff`",
    "invalidValue" : "'MINUTE'",
    "parameter" : "`unit`"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 94,
    "fragment" : "timestampdiff('MINUTE', timestamp'2022-02-14 01:02:03', timestamp'2022-02-14 02:00:03')"
  } ]
}


-- !query
select timestampdiff('YEAR', date'2022-02-15', date'2023-02-15')
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "INVALID_PARAMETER_VALUE.DATETIME_UNIT",
  "sqlState" : "22023",
  "messageParameters" : {
    "functionName" : "`timestampdiff`",
    "invalidValue" : "'YEAR'",
    "parameter" : "`unit`"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 64,
    "fragment" : "timestampdiff('YEAR', date'2022-02-15', date'2023-02-15')"
  } ]
}


-- !query
select timediff(QUARTER, timestamp'2023-08-10 01:02:03', timestamp'2022-01-14 01:02:03')
-- !query schema
struct<timestampdiff(QUARTER, TIMESTAMP '2023-08-10 01:02:03', TIMESTAMP '2022-01-14 01:02:03'):bigint>
-- !query output
-6


-- !query
select timediff(HOUR, timestamp'2022-02-14 01:02:03', timestamp'2022-02-14 12:00:03')
-- !query schema
struct<timestampdiff(HOUR, TIMESTAMP '2022-02-14 01:02:03', TIMESTAMP '2022-02-14 12:00:03'):bigint>
-- !query output
10


-- !query
select timediff(DAY, date'2022-02-15', date'2023-02-15')
-- !query schema
struct<timestampdiff(DAY, DATE '2022-02-15', DATE '2023-02-15'):bigint>
-- !query output
365


-- !query
select timediff(SECOND, date'2022-02-15', timestamp'2022-02-14 23:59:59')
-- !query schema
struct<timestampdiff(SECOND, DATE '2022-02-15', TIMESTAMP '2022-02-14 23:59:59'):bigint>
-- !query output
-1


-- !query
select timediff('MINUTE', timestamp'2023-02-14 01:02:03', timestamp'2023-02-14 02:00:03')
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "INVALID_PARAMETER_VALUE.DATETIME_UNIT",
  "sqlState" : "22023",
  "messageParameters" : {
    "functionName" : "`timediff`",
    "invalidValue" : "'MINUTE'",
    "parameter" : "`unit`"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 89,
    "fragment" : "timediff('MINUTE', timestamp'2023-02-14 01:02:03', timestamp'2023-02-14 02:00:03')"
  } ]
}


-- !query
select timediff('YEAR', date'2020-02-15', date'2023-02-15')
-- !query schema
struct<>
-- !query output
org.apache.spark.sql.catalyst.parser.ParseException
{
  "errorClass" : "INVALID_PARAMETER_VALUE.DATETIME_UNIT",
  "sqlState" : "22023",
  "messageParameters" : {
    "functionName" : "`timediff`",
    "invalidValue" : "'YEAR'",
    "parameter" : "`unit`"
  },
  "queryContext" : [ {
    "objectType" : "",
    "objectName" : "",
    "startIndex" : 8,
    "stopIndex" : 59,
    "fragment" : "timediff('YEAR', date'2020-02-15', date'2023-02-15')"
  } ]
}
