== Physical Plan ==
TakeOrderedAndProject (33)
+- * Project (32)
   +- * SortMergeJoin Inner (31)
      :- * Sort (25)
      :  +- Exchange (24)
      :     +- * HashAggregate (23)
      :        +- Exchange (22)
      :           +- * HashAggregate (21)
      :              +- * Project (20)
      :                 +- * BroadcastHashJoin Inner BuildRight (19)
      :                    :- * Project (13)
      :                    :  +- * BroadcastHashJoin Inner BuildRight (12)
      :                    :     :- * Project (6)
      :                    :     :  +- * BroadcastHashJoin Inner BuildRight (5)
      :                    :     :     :- * Filter (3)
      :                    :     :     :  +- * ColumnarToRow (2)
      :                    :     :     :     +- Scan parquet spark_catalog.default.store_sales (1)
      :                    :     :     +- ReusedExchange (4)
      :                    :     +- BroadcastExchange (11)
      :                    :        +- * Project (10)
      :                    :           +- * Filter (9)
      :                    :              +- * ColumnarToRow (8)
      :                    :                 +- Scan parquet spark_catalog.default.household_demographics (7)
      :                    +- BroadcastExchange (18)
      :                       +- * Project (17)
      :                          +- * Filter (16)
      :                             +- * ColumnarToRow (15)
      :                                +- <PERSON>an parquet spark_catalog.default.store (14)
      +- * Sort (30)
         +- Exchange (29)
            +- * Filter (28)
               +- * ColumnarToRow (27)
                  +- Scan parquet spark_catalog.default.customer (26)


(1) Scan parquet spark_catalog.default.store_sales
Output [8]: [ss_customer_sk#1, ss_hdemo_sk#2, ss_addr_sk#3, ss_store_sk#4, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7, ss_sold_date_sk#8]
Batched: true
Location: InMemoryFileIndex []
PartitionFilters: [isnotnull(ss_sold_date_sk#8), dynamicpruningexpression(ss_sold_date_sk#8 IN dynamicpruning#9)]
PushedFilters: [IsNotNull(ss_store_sk), IsNotNull(ss_hdemo_sk), IsNotNull(ss_customer_sk)]
ReadSchema: struct<ss_customer_sk:int,ss_hdemo_sk:int,ss_addr_sk:int,ss_store_sk:int,ss_ticket_number:int,ss_coupon_amt:decimal(7,2),ss_net_profit:decimal(7,2)>

(2) ColumnarToRow [codegen id : 4]
Input [8]: [ss_customer_sk#1, ss_hdemo_sk#2, ss_addr_sk#3, ss_store_sk#4, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7, ss_sold_date_sk#8]

(3) Filter [codegen id : 4]
Input [8]: [ss_customer_sk#1, ss_hdemo_sk#2, ss_addr_sk#3, ss_store_sk#4, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7, ss_sold_date_sk#8]
Condition : ((isnotnull(ss_store_sk#4) AND isnotnull(ss_hdemo_sk#2)) AND isnotnull(ss_customer_sk#1))

(4) ReusedExchange [Reuses operator id: 38]
Output [1]: [d_date_sk#10]

(5) BroadcastHashJoin [codegen id : 4]
Left keys [1]: [ss_sold_date_sk#8]
Right keys [1]: [d_date_sk#10]
Join type: Inner
Join condition: None

(6) Project [codegen id : 4]
Output [7]: [ss_customer_sk#1, ss_hdemo_sk#2, ss_addr_sk#3, ss_store_sk#4, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7]
Input [9]: [ss_customer_sk#1, ss_hdemo_sk#2, ss_addr_sk#3, ss_store_sk#4, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7, ss_sold_date_sk#8, d_date_sk#10]

(7) Scan parquet spark_catalog.default.household_demographics
Output [3]: [hd_demo_sk#11, hd_dep_count#12, hd_vehicle_count#13]
Batched: true
Location [not included in comparison]/{warehouse_dir}/household_demographics]
PushedFilters: [Or(EqualTo(hd_dep_count,6),GreaterThan(hd_vehicle_count,2)), IsNotNull(hd_demo_sk)]
ReadSchema: struct<hd_demo_sk:int,hd_dep_count:int,hd_vehicle_count:int>

(8) ColumnarToRow [codegen id : 2]
Input [3]: [hd_demo_sk#11, hd_dep_count#12, hd_vehicle_count#13]

(9) Filter [codegen id : 2]
Input [3]: [hd_demo_sk#11, hd_dep_count#12, hd_vehicle_count#13]
Condition : (((hd_dep_count#12 = 6) OR (hd_vehicle_count#13 > 2)) AND isnotnull(hd_demo_sk#11))

(10) Project [codegen id : 2]
Output [1]: [hd_demo_sk#11]
Input [3]: [hd_demo_sk#11, hd_dep_count#12, hd_vehicle_count#13]

(11) BroadcastExchange
Input [1]: [hd_demo_sk#11]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, true] as bigint)),false), [plan_id=1]

(12) BroadcastHashJoin [codegen id : 4]
Left keys [1]: [ss_hdemo_sk#2]
Right keys [1]: [hd_demo_sk#11]
Join type: Inner
Join condition: None

(13) Project [codegen id : 4]
Output [6]: [ss_customer_sk#1, ss_addr_sk#3, ss_store_sk#4, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7]
Input [8]: [ss_customer_sk#1, ss_hdemo_sk#2, ss_addr_sk#3, ss_store_sk#4, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7, hd_demo_sk#11]

(14) Scan parquet spark_catalog.default.store
Output [3]: [s_store_sk#14, s_number_employees#15, s_city#16]
Batched: true
Location [not included in comparison]/{warehouse_dir}/store]
PushedFilters: [IsNotNull(s_number_employees), GreaterThanOrEqual(s_number_employees,200), LessThanOrEqual(s_number_employees,295), IsNotNull(s_store_sk)]
ReadSchema: struct<s_store_sk:int,s_number_employees:int,s_city:string>

(15) ColumnarToRow [codegen id : 3]
Input [3]: [s_store_sk#14, s_number_employees#15, s_city#16]

(16) Filter [codegen id : 3]
Input [3]: [s_store_sk#14, s_number_employees#15, s_city#16]
Condition : (((isnotnull(s_number_employees#15) AND (s_number_employees#15 >= 200)) AND (s_number_employees#15 <= 295)) AND isnotnull(s_store_sk#14))

(17) Project [codegen id : 3]
Output [2]: [s_store_sk#14, s_city#16]
Input [3]: [s_store_sk#14, s_number_employees#15, s_city#16]

(18) BroadcastExchange
Input [2]: [s_store_sk#14, s_city#16]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, true] as bigint)),false), [plan_id=2]

(19) BroadcastHashJoin [codegen id : 4]
Left keys [1]: [ss_store_sk#4]
Right keys [1]: [s_store_sk#14]
Join type: Inner
Join condition: None

(20) Project [codegen id : 4]
Output [6]: [ss_customer_sk#1, ss_addr_sk#3, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7, s_city#16]
Input [8]: [ss_customer_sk#1, ss_addr_sk#3, ss_store_sk#4, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7, s_store_sk#14, s_city#16]

(21) HashAggregate [codegen id : 4]
Input [6]: [ss_customer_sk#1, ss_addr_sk#3, ss_ticket_number#5, ss_coupon_amt#6, ss_net_profit#7, s_city#16]
Keys [4]: [ss_ticket_number#5, ss_customer_sk#1, ss_addr_sk#3, s_city#16]
Functions [2]: [partial_sum(UnscaledValue(ss_coupon_amt#6)), partial_sum(UnscaledValue(ss_net_profit#7))]
Aggregate Attributes [2]: [sum#17, sum#18]
Results [6]: [ss_ticket_number#5, ss_customer_sk#1, ss_addr_sk#3, s_city#16, sum#19, sum#20]

(22) Exchange
Input [6]: [ss_ticket_number#5, ss_customer_sk#1, ss_addr_sk#3, s_city#16, sum#19, sum#20]
Arguments: hashpartitioning(ss_ticket_number#5, ss_customer_sk#1, ss_addr_sk#3, s_city#16, 5), ENSURE_REQUIREMENTS, [plan_id=3]

(23) HashAggregate [codegen id : 5]
Input [6]: [ss_ticket_number#5, ss_customer_sk#1, ss_addr_sk#3, s_city#16, sum#19, sum#20]
Keys [4]: [ss_ticket_number#5, ss_customer_sk#1, ss_addr_sk#3, s_city#16]
Functions [2]: [sum(UnscaledValue(ss_coupon_amt#6)), sum(UnscaledValue(ss_net_profit#7))]
Aggregate Attributes [2]: [sum(UnscaledValue(ss_coupon_amt#6))#21, sum(UnscaledValue(ss_net_profit#7))#22]
Results [5]: [ss_ticket_number#5, ss_customer_sk#1, s_city#16, MakeDecimal(sum(UnscaledValue(ss_coupon_amt#6))#21,17,2) AS amt#23, MakeDecimal(sum(UnscaledValue(ss_net_profit#7))#22,17,2) AS profit#24]

(24) Exchange
Input [5]: [ss_ticket_number#5, ss_customer_sk#1, s_city#16, amt#23, profit#24]
Arguments: hashpartitioning(ss_customer_sk#1, 5), ENSURE_REQUIREMENTS, [plan_id=4]

(25) Sort [codegen id : 6]
Input [5]: [ss_ticket_number#5, ss_customer_sk#1, s_city#16, amt#23, profit#24]
Arguments: [ss_customer_sk#1 ASC NULLS FIRST], false, 0

(26) Scan parquet spark_catalog.default.customer
Output [3]: [c_customer_sk#25, c_first_name#26, c_last_name#27]
Batched: true
Location [not included in comparison]/{warehouse_dir}/customer]
PushedFilters: [IsNotNull(c_customer_sk)]
ReadSchema: struct<c_customer_sk:int,c_first_name:string,c_last_name:string>

(27) ColumnarToRow [codegen id : 7]
Input [3]: [c_customer_sk#25, c_first_name#26, c_last_name#27]

(28) Filter [codegen id : 7]
Input [3]: [c_customer_sk#25, c_first_name#26, c_last_name#27]
Condition : isnotnull(c_customer_sk#25)

(29) Exchange
Input [3]: [c_customer_sk#25, c_first_name#26, c_last_name#27]
Arguments: hashpartitioning(c_customer_sk#25, 5), ENSURE_REQUIREMENTS, [plan_id=5]

(30) Sort [codegen id : 8]
Input [3]: [c_customer_sk#25, c_first_name#26, c_last_name#27]
Arguments: [c_customer_sk#25 ASC NULLS FIRST], false, 0

(31) SortMergeJoin [codegen id : 9]
Left keys [1]: [ss_customer_sk#1]
Right keys [1]: [c_customer_sk#25]
Join type: Inner
Join condition: None

(32) Project [codegen id : 9]
Output [7]: [c_last_name#27, c_first_name#26, substr(s_city#16, 1, 30) AS substr(s_city, 1, 30)#28, ss_ticket_number#5, amt#23, profit#24, s_city#16]
Input [8]: [ss_ticket_number#5, ss_customer_sk#1, s_city#16, amt#23, profit#24, c_customer_sk#25, c_first_name#26, c_last_name#27]

(33) TakeOrderedAndProject
Input [7]: [c_last_name#27, c_first_name#26, substr(s_city, 1, 30)#28, ss_ticket_number#5, amt#23, profit#24, s_city#16]
Arguments: 100, [c_last_name#27 ASC NULLS FIRST, c_first_name#26 ASC NULLS FIRST, substr(s_city#16, 1, 30) ASC NULLS FIRST, profit#24 ASC NULLS FIRST], [c_last_name#27, c_first_name#26, substr(s_city, 1, 30)#28, ss_ticket_number#5, amt#23, profit#24]

===== Subqueries =====

Subquery:1 Hosting operator id = 1 Hosting Expression = ss_sold_date_sk#8 IN dynamicpruning#9
BroadcastExchange (38)
+- * Project (37)
   +- * Filter (36)
      +- * ColumnarToRow (35)
         +- Scan parquet spark_catalog.default.date_dim (34)


(34) Scan parquet spark_catalog.default.date_dim
Output [3]: [d_date_sk#10, d_year#29, d_dow#30]
Batched: true
Location [not included in comparison]/{warehouse_dir}/date_dim]
PushedFilters: [IsNotNull(d_dow), EqualTo(d_dow,1), In(d_year, [1999,2000,2001]), IsNotNull(d_date_sk)]
ReadSchema: struct<d_date_sk:int,d_year:int,d_dow:int>

(35) ColumnarToRow [codegen id : 1]
Input [3]: [d_date_sk#10, d_year#29, d_dow#30]

(36) Filter [codegen id : 1]
Input [3]: [d_date_sk#10, d_year#29, d_dow#30]
Condition : (((isnotnull(d_dow#30) AND (d_dow#30 = 1)) AND d_year#29 IN (1999,2000,2001)) AND isnotnull(d_date_sk#10))

(37) Project [codegen id : 1]
Output [1]: [d_date_sk#10]
Input [3]: [d_date_sk#10, d_year#29, d_dow#30]

(38) BroadcastExchange
Input [1]: [d_date_sk#10]
Arguments: HashedRelationBroadcastMode(List(cast(input[0, int, true] as bigint)),false), [plan_id=6]


