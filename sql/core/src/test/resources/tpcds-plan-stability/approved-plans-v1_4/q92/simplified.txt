WholeStageCodegen (7)
  HashAggregate [sum] [sum(UnscaledValue(ws_ext_discount_amt)),Excess Discount Amount ,sum]
    InputAdapter
      Exchange #1
        WholeStageCodegen (6)
          HashAggregate [ws_ext_discount_amt] [sum,sum]
            Project [ws_ext_discount_amt]
              BroadcastHashJoin [ws_sold_date_sk,d_date_sk]
                Project [ws_ext_discount_amt,ws_sold_date_sk]
                  BroadcastHashJoin [i_item_sk,ws_item_sk,ws_ext_discount_amt,(1.3 * avg(ws_ext_discount_amt))]
                    Project [ws_ext_discount_amt,ws_sold_date_sk,i_item_sk]
                      BroadcastHashJoin [ws_item_sk,i_item_sk]
                        Filter [ws_item_sk,ws_ext_discount_amt]
                          ColumnarToRow
                            InputAdapter
                              Scan parquet spark_catalog.default.web_sales [ws_item_sk,ws_ext_discount_amt,ws_sold_date_sk]
                                SubqueryBroadcast [d_date_sk] #1
                                  BroadcastExchange #2
                                    WholeStageCodegen (1)
                                      Project [d_date_sk]
                                        Filter [d_date,d_date_sk]
                                          ColumnarToRow
                                            InputAdapter
                                              Scan parquet spark_catalog.default.date_dim [d_date_sk,d_date]
                        InputAdapter
                          BroadcastExchange #3
                            WholeStageCodegen (1)
                              Project [i_item_sk]
                                Filter [i_manufact_id,i_item_sk]
                                  ColumnarToRow
                                    InputAdapter
                                      Scan parquet spark_catalog.default.item [i_item_sk,i_manufact_id]
                    InputAdapter
                      BroadcastExchange #4
                        WholeStageCodegen (4)
                          Filter [(1.3 * avg(ws_ext_discount_amt))]
                            HashAggregate [ws_item_sk,sum,count] [avg(UnscaledValue(ws_ext_discount_amt)),(1.3 * avg(ws_ext_discount_amt)),sum,count]
                              InputAdapter
                                Exchange [ws_item_sk] #5
                                  WholeStageCodegen (3)
                                    HashAggregate [ws_item_sk,ws_ext_discount_amt] [sum,count,sum,count]
                                      Project [ws_item_sk,ws_ext_discount_amt]
                                        BroadcastHashJoin [ws_sold_date_sk,d_date_sk]
                                          Filter [ws_item_sk]
                                            ColumnarToRow
                                              InputAdapter
                                                Scan parquet spark_catalog.default.web_sales [ws_item_sk,ws_ext_discount_amt,ws_sold_date_sk]
                                                  ReusedSubquery [d_date_sk] #1
                                          InputAdapter
                                            ReusedExchange [d_date_sk] #2
                InputAdapter
                  ReusedExchange [d_date_sk] #2
