



CREATE TABLE TEST3a(A INT, B DOUBLE) STORED AS TEXTFILE; 
DESCRIBE TEST3a; 
CREATE TABLE TEST3b(A ARRAY<INT>, B DOUBLE, C MAP<DOUBLE, INT>) STORED AS TEXTFILE; 
DESC<PERSON>BE TEST3b; 
SHOW TABLES;
EXPLAIN
ALTER TABLE TEST3b ADD COLUMNS (X DOUBLE);
ALTER TABLE TEST3b ADD COLUMNS (X DOUBLE);
DESCRIBE TEST3b; 
EXPLAIN
ALTER TABLE TEST3b RENAME TO TEST3c;
ALTER TABLE TEST3b RENAME TO TEST3c;
DE<PERSON><PERSON><PERSON> TEST3c; 
SHOW TABLES;
EXPLAIN
ALTER TABLE TEST3c REPLACE COLUMNS (R1 INT, R2 DOUBLE);
ALTER TABLE TEST3c REPLACE COLUMNS (R1 INT, R2 DOUBLE);
DESCRIBE EXTENDED TEST3c;




