{"common": {"planId": "1"}, "aggregate": {"input": {"common": {"planId": "0"}, "localRelation": {"schema": "struct<id:bigint,a:int,b:double>"}}, "groupType": "GROUP_TYPE_ROLLUP", "groupingExpressions": [{"unresolvedAttribute": {"unparsedIdentifier": "a"}, "common": {"origin": {"jvmOrigin": {"stackTrace": [{"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.Column$", "methodName": "apply", "fileName": "Column.scala"}, {"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.PlanGenerationTestSuite", "methodName": "~~trimmed~anonfun~~", "fileName": "PlanGenerationTestSuite.scala"}]}}}}, {"unresolvedAttribute": {"unparsedIdentifier": "b"}, "common": {"origin": {"jvmOrigin": {"stackTrace": [{"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.Column$", "methodName": "apply", "fileName": "Column.scala"}, {"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.PlanGenerationTestSuite", "methodName": "~~trimmed~anonfun~~", "fileName": "PlanGenerationTestSuite.scala"}]}}}}], "aggregateExpressions": [{"alias": {"expr": {"unresolvedFunction": {"functionName": "count", "arguments": [{"literal": {"integer": 1}, "common": {"origin": {"jvmOrigin": {"stackTrace": [{"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.functions$", "methodName": "lit", "fileName": "functions.scala"}, {"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.RelationalGroupedDataset", "methodName": "count", "fileName": "RelationalGroupedDataset.scala"}]}}}}], "isInternal": false}, "common": {"origin": {"jvmOrigin": {"stackTrace": [{"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.functions$", "methodName": "count", "fileName": "functions.scala"}, {"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.RelationalGroupedDataset", "methodName": "count", "fileName": "RelationalGroupedDataset.scala"}]}}}}, "name": ["count"]}, "common": {"origin": {"jvmOrigin": {"stackTrace": [{"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.Column", "methodName": "as", "fileName": "Column.scala"}, {"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.RelationalGroupedDataset", "methodName": "count", "fileName": "RelationalGroupedDataset.scala"}]}}}}]}}