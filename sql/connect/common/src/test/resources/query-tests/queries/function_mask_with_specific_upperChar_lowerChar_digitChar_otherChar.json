{"common": {"planId": "1"}, "project": {"input": {"common": {"planId": "0"}, "localRelation": {"schema": "struct<id:bigint,a:int,b:double,d:struct<id:bigint,a:int,b:double>,e:array<int>,f:map<string,struct<id:bigint,a:int,b:double>>,g:string>"}}, "expressions": [{"unresolvedFunction": {"functionName": "mask", "arguments": [{"unresolvedAttribute": {"unparsedIdentifier": "g"}, "common": {"origin": {"jvmOrigin": {"stackTrace": [{"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.functions$", "methodName": "col", "fileName": "functions.scala"}, {"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.PlanGenerationTestSuite", "methodName": "~~trimmed~anonfun~~", "fileName": "PlanGenerationTestSuite.scala"}]}}}}, {"literal": {"string": "X"}, "common": {"origin": {"jvmOrigin": {"stackTrace": [{"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.functions$", "methodName": "lit", "fileName": "functions.scala"}, {"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.PlanGenerationTestSuite", "methodName": "~~trimmed~anonfun~~", "fileName": "PlanGenerationTestSuite.scala"}]}}}}, {"literal": {"string": "x"}, "common": {"origin": {"jvmOrigin": {"stackTrace": [{"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.functions$", "methodName": "lit", "fileName": "functions.scala"}, {"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.PlanGenerationTestSuite", "methodName": "~~trimmed~anonfun~~", "fileName": "PlanGenerationTestSuite.scala"}]}}}}, {"literal": {"string": "n"}, "common": {"origin": {"jvmOrigin": {"stackTrace": [{"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.functions$", "methodName": "lit", "fileName": "functions.scala"}, {"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.PlanGenerationTestSuite", "methodName": "~~trimmed~anonfun~~", "fileName": "PlanGenerationTestSuite.scala"}]}}}}, {"literal": {"string": "*"}, "common": {"origin": {"jvmOrigin": {"stackTrace": [{"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.functions$", "methodName": "lit", "fileName": "functions.scala"}, {"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.PlanGenerationTestSuite", "methodName": "~~trimmed~anonfun~~", "fileName": "PlanGenerationTestSuite.scala"}]}}}}], "isInternal": false}, "common": {"origin": {"jvmOrigin": {"stackTrace": [{"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.functions$", "methodName": "mask", "fileName": "functions.scala"}, {"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.PlanGenerationTestSuite", "methodName": "~~trimmed~anonfun~~", "fileName": "PlanGenerationTestSuite.scala"}]}}}}]}}