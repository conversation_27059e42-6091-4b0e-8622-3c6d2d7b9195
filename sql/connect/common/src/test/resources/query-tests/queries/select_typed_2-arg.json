{"common": {"planId": "1"}, "project": {"input": {"common": {"planId": "0"}, "localRelation": {"schema": "struct<id:bigint,a:int,b:double>"}}, "expressions": [{"unresolvedFunction": {"functionName": "struct", "arguments": [{"unresolvedAttribute": {"unparsedIdentifier": "id"}, "common": {"origin": {"jvmOrigin": {"stackTrace": [{"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.functions$", "methodName": "col", "fileName": "functions.scala"}, {"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.PlanGenerationTestSuite", "methodName": "~~trimmed~anonfun~~", "fileName": "PlanGenerationTestSuite.scala"}]}}}}, {"unresolvedAttribute": {"unparsedIdentifier": "a"}, "common": {"origin": {"jvmOrigin": {"stackTrace": [{"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.functions$", "methodName": "col", "fileName": "functions.scala"}, {"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.PlanGenerationTestSuite", "methodName": "~~trimmed~anonfun~~", "fileName": "PlanGenerationTestSuite.scala"}]}}}}], "isInternal": false}, "common": {"origin": {"jvmOrigin": {"stackTrace": [{"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.functions$", "methodName": "struct", "fileName": "functions.scala"}, {"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.PlanGenerationTestSuite", "methodName": "~~trimmed~anonfun~~", "fileName": "PlanGenerationTestSuite.scala"}]}}}}, {"unresolvedFunction": {"functionName": "struct", "arguments": [{"unresolvedAttribute": {"unparsedIdentifier": "a"}, "common": {"origin": {"jvmOrigin": {"stackTrace": [{"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.functions$", "methodName": "col", "fileName": "functions.scala"}, {"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.PlanGenerationTestSuite", "methodName": "~~trimmed~anonfun~~", "fileName": "PlanGenerationTestSuite.scala"}]}}}}, {"unresolvedAttribute": {"unparsedIdentifier": "b"}, "common": {"origin": {"jvmOrigin": {"stackTrace": [{"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.functions$", "methodName": "col", "fileName": "functions.scala"}, {"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.PlanGenerationTestSuite", "methodName": "~~trimmed~anonfun~~", "fileName": "PlanGenerationTestSuite.scala"}]}}}}], "isInternal": false}, "common": {"origin": {"jvmOrigin": {"stackTrace": [{"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.functions$", "methodName": "struct", "fileName": "functions.scala"}, {"classLoaderName": "app", "declaringClass": "org.apache.spark.sql.PlanGenerationTestSuite", "methodName": "~~trimmed~anonfun~~", "fileName": "PlanGenerationTestSuite.scala"}]}}}}]}}